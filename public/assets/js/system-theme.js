/**
 * System Theme Detection and Management
 * Adds support for automatic OS theme detection alongside manual light/dark theme options
 */

(function() {
    'use strict';

    // System theme detection and management
    let systemThemeBtn;
    let systemThemeMediaQuery;
    let isSystemThemeActive = false;

    // Initialize system theme functionality
    function initSystemTheme() {
        systemThemeBtn = document.querySelector('#switcher-system-theme');
        
        // Create media query for system theme detection
        systemThemeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        // Add event listener for system theme button
        if (systemThemeBtn) {
            systemThemeBtn.addEventListener('click', () => {
                systemThemeFn();
                localStorage.setItem("ynexSystemTheme", 'true');
                localStorage.removeItem("ynexdarktheme");
                localStorage.removeItem("ynexlighttheme");
            });
        }

        // Listen for system theme changes
        systemThemeMediaQuery.addEventListener('change', handleSystemThemeChange);

        // Check if system theme was previously selected
        if (localStorage.getItem("ynexSystemTheme") === 'true') {
            systemThemeFn();
        }
    }

    // Apply system theme based on OS preference
    function systemThemeFn() {
        isSystemThemeActive = true;
        let html = document.querySelector('html');
        
        // Check system preference and apply appropriate theme
        if (systemThemeMediaQuery.matches) {
            // System is in dark mode
            applyDarkTheme();
        } else {
            // System is in light mode
            applyLightTheme();
        }

        // Update UI to show system theme is selected
        if (systemThemeBtn) {
            systemThemeBtn.checked = true;
        }
        
        // Uncheck other theme options
        const lightBtn = document.querySelector('#switcher-light-theme');
        const darkBtn = document.querySelector('#switcher-dark-theme');
        if (lightBtn) lightBtn.checked = false;
        if (darkBtn) darkBtn.checked = false;

        // Update localStorage
        localStorage.setItem("ynexSystemTheme", 'true');
        localStorage.removeItem("ynexdarktheme");
        localStorage.removeItem("ynexlighttheme");
    }

    // Handle system theme changes when user changes OS theme
    function handleSystemThemeChange(e) {
        if (isSystemThemeActive && localStorage.getItem("ynexSystemTheme") === 'true') {
            if (e.matches) {
                // System switched to dark mode
                applyDarkTheme();
            } else {
                // System switched to light mode
                applyLightTheme();
            }
        }
    }

    // Apply dark theme styles
    function applyDarkTheme() {
        let html = document.querySelector('html');
        html.setAttribute('data-theme-mode', 'dark');
        html.setAttribute('data-header-styles', 'dark');
        html.setAttribute('data-menu-styles', 'dark');
        
        if (!localStorage.getItem('primaryRGB')) {
            html.setAttribute('style', '');
        }
        
        // Update menu and header checkboxes
        const menuDarkBtn = document.querySelector('#switcher-menu-dark');
        const headerDarkBtn = document.querySelector('#switcher-header-dark');
        if (menuDarkBtn) menuDarkBtn.checked = true;
        if (headerDarkBtn) headerDarkBtn.checked = true;

        // Clean up any custom background styles
        html.style.removeProperty('--body-bg-rgb');
        html.style.removeProperty('--body-bg-rgb2');
        html.style.removeProperty('--light-rgb');
        html.style.removeProperty('--form-control-bg');
        html.style.removeProperty('--input-border');

        // Update colors if updateColors function exists
        if (typeof updateColors === 'function') {
            updateColors();
        }
    }

    // Apply light theme styles
    function applyLightTheme() {
        let html = document.querySelector('html');
        html.setAttribute('data-theme-mode', 'light');
        html.setAttribute('data-header-styles', 'light');
        
        if (localStorage.getItem("ynexlayout") == "horizontal") {
            html.setAttribute('data-menu-styles', 'light');
        }
        
        if (!localStorage.getItem('primaryRGB')) {
            html.setAttribute('style', '');
        }

        // Update menu and header checkboxes
        const menuLightBtn = document.querySelector('#switcher-menu-light');
        const headerLightBtn = document.querySelector('#switcher-header-light');
        if (menuLightBtn) menuLightBtn.checked = true;
        if (headerLightBtn) headerLightBtn.checked = true;

        // Clean up any custom background styles
        html.style.removeProperty('--body-bg-rgb');
        html.style.removeProperty('--body-bg-rgb2');
        html.style.removeProperty('--light-rgb');
        html.style.removeProperty('--form-control-bg');
        html.style.removeProperty('--input-border');

        // Update colors if updateColors function exists
        if (typeof updateColors === 'function') {
            updateColors();
        }
    }

    // Override existing light and dark theme functions to disable system theme
    function overrideExistingThemeFunctions() {
        // Store original functions if they exist
        const originalLightFn = window.lightFn;
        const originalDarkFn = window.darkFn;

        // Override lightFn to disable system theme
        window.lightFn = function() {
            isSystemThemeActive = false;
            localStorage.removeItem("ynexSystemTheme");
            
            if (systemThemeBtn) {
                systemThemeBtn.checked = false;
            }
            
            if (originalLightFn) {
                originalLightFn();
            }
        };

        // Override darkFn to disable system theme
        window.darkFn = function() {
            isSystemThemeActive = false;
            localStorage.removeItem("ynexSystemTheme");
            
            if (systemThemeBtn) {
                systemThemeBtn.checked = false;
            }
            
            if (originalDarkFn) {
                originalDarkFn();
            }
        };
    }

    // Update checkOptions function to handle system theme
    function updateCheckOptions() {
        const originalCheckOptions = window.checkOptions;
        
        window.checkOptions = function() {
            // Call original checkOptions if it exists
            if (originalCheckOptions) {
                originalCheckOptions();
            }
            
            // Check system theme
            if (localStorage.getItem("ynexSystemTheme") === 'true') {
                if (systemThemeBtn) {
                    systemThemeBtn.checked = true;
                }
                isSystemThemeActive = true;
            }
        };
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            initSystemTheme();
            overrideExistingThemeFunctions();
            updateCheckOptions();
        });
    } else {
        initSystemTheme();
        overrideExistingThemeFunctions();
        updateCheckOptions();
    }

    // Export functions for external use
    window.systemThemeFn = systemThemeFn;
    window.isSystemThemeActive = function() { return isSystemThemeActive; };

})();
