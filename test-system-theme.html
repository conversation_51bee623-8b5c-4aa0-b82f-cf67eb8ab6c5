<!DOCTYPE html>
<html lang="en" dir="ltr" data-nav-layout="vertical" data-theme-mode="light" data-header-styles="light" data-menu-styles="dark" data-toggled="close">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Theme Test</title>
    <link href="public/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="public/assets/css/styles.min.css" rel="stylesheet">
    <link href="public/assets/css/icons.css" rel="stylesheet">
    <style>
        .theme-test {
            padding: 2rem;
            margin: 2rem;
            border: 1px solid var(--default-border);
            border-radius: 8px;
            background: var(--body-bg);
            color: var(--default-text-color);
        }
        .current-theme {
            font-weight: bold;
            color: var(--primary);
        }
    </style>
</head>
<body>
    <!-- Theme Switcher -->
    <div class="container mt-4">
        <div class="card">
            <div class="card-header">
                <h5>System Theme Test</h5>
            </div>
            <div class="card-body">
                <div class="theme-test">
                    <h6>Current Theme: <span id="current-theme" class="current-theme">Loading...</span></h6>
                    <p>This page will automatically follow your system theme preference when "System" is selected.</p>
                    <p>Try changing your OS theme (dark/light mode) to see the automatic switching in action.</p>
                </div>
                
                <div class="row switcher-style gx-0 mt-3">
                    <div class="col-4">
                        <div class="form-check switch-select">
                            <label class="form-check-label" for="switcher-light-theme">
                                Light
                            </label>
                            <input class="form-check-input" type="radio" name="theme-style" id="switcher-light-theme">
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="form-check switch-select">
                            <label class="form-check-label" for="switcher-dark-theme">
                                Dark
                            </label>
                            <input class="form-check-input" type="radio" name="theme-style" id="switcher-dark-theme">
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="form-check switch-select">
                            <label class="form-check-label" for="switcher-system-theme">
                                System
                            </label>
                            <input class="form-check-input" type="radio" name="theme-style" id="switcher-system-theme">
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button id="clear-storage" class="btn btn-outline-secondary btn-sm">Clear Theme Storage</button>
                    <button id="check-storage" class="btn btn-outline-info btn-sm">Check Storage</button>
                </div>
                
                <div id="storage-info" class="mt-3 p-2 bg-light rounded" style="display: none;">
                    <small id="storage-content"></small>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="public/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="public/assets/js/main.js"></script>
    <script src="public/assets/js/system-theme.js"></script>
    
    <script>
        // Update current theme display
        function updateThemeDisplay() {
            const html = document.querySelector('html');
            const themeMode = html.getAttribute('data-theme-mode');
            const currentThemeEl = document.getElementById('current-theme');
            
            if (localStorage.getItem('ynexSystemTheme') === 'true') {
                currentThemeEl.textContent = `System (${themeMode})`;
            } else if (localStorage.getItem('ynexdarktheme')) {
                currentThemeEl.textContent = 'Dark';
            } else {
                currentThemeEl.textContent = 'Light';
            }
        }
        
        // Clear storage button
        document.getElementById('clear-storage').addEventListener('click', function() {
            localStorage.removeItem('ynexSystemTheme');
            localStorage.removeItem('ynexdarktheme');
            localStorage.removeItem('ynexlighttheme');
            location.reload();
        });
        
        // Check storage button
        document.getElementById('check-storage').addEventListener('click', function() {
            const storageInfo = document.getElementById('storage-info');
            const storageContent = document.getElementById('storage-content');
            
            const info = {
                'ynexSystemTheme': localStorage.getItem('ynexSystemTheme'),
                'ynexdarktheme': localStorage.getItem('ynexdarktheme'),
                'ynexlighttheme': localStorage.getItem('ynexlighttheme'),
                'System prefers dark': window.matchMedia('(prefers-color-scheme: dark)').matches
            };
            
            storageContent.textContent = JSON.stringify(info, null, 2);
            storageInfo.style.display = 'block';
        });
        
        // Listen for theme changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme-mode') {
                    updateThemeDisplay();
                }
            });
        });
        
        observer.observe(document.querySelector('html'), {
            attributes: true,
            attributeFilter: ['data-theme-mode']
        });
        
        // Initial display update
        setTimeout(updateThemeDisplay, 100);
    </script>
</body>
</html>
